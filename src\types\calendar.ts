import { Timestamp } from 'firebase/firestore';

/**
 * Types de traitement disponibles pour les plantes
 */
export type TreatmentType = 'fertilisation' | 'traitement' | 'arrosage' | 'rempotage' | 'taille';

/**
 * Source de création de l'événement
 */
export type EventCreatedBy = 'user' | 'system' | 'gemini';

/**
 * Statut de l'événement
 */
export type EventStatus = 'scheduled' | 'completed' | 'skipped' | 'overdue';

/**
 * Fréquence de récurrence
 */
export type RecurrenceFrequency = 'daily' | 'weekly' | 'monthly';

/**
 * Interface pour définir un pattern de récurrence
 */
export interface RecurrencePattern {
  /** Fréquence de récurrence (quotidienne, hebdomadaire, mensuelle) */
  frequency: RecurrenceFrequency;
  /** Intervalle entre les occurrences (ex: tous les X jours/semaines/mois) */
  interval: number;
  /** Date de fin de la récurrence (optionnelle) */
  endDate?: Date;
  /** Nombre maximum d'occurrences (optionnel) */
  maxOccurrences?: number;
}

/**
 * Interface principale pour un événement de calendrier
 */
export interface CalendarEvent {
  /** Identifiant unique de l'événement */
  id: string;
  /** Identifiant de la plante concernée */
  plantId: string;
  /** Nom de la plante pour affichage */
  plantName: string;
  /** Type de traitement à effectuer */
  treatmentType: TreatmentType;
  /** Titre de l'événement */
  title: string;
  /** Description détaillée de l'événement */
  description: string;
  /** Date et heure de début */
  startDate: Date;
  /** Date et heure de fin */
  endDate: Date;
  /** Indique si l'événement est récurrent */
  isRecurring: boolean;
  /** Pattern de récurrence (si applicable) */
  recurrencePattern?: RecurrencePattern;

  /** Source de création de l'événement */
  createdBy: EventCreatedBy;
  /** Statut actuel de l'événement */
  status: EventStatus;
  /** Date de création de l'événement */
  createdAt: Timestamp;
  /** Date de dernière modification */
  updatedAt: Timestamp;
}

/**
 * Interface pour les paramètres de notifications de l'utilisateur
 */
export interface NotificationSettings {
  /** Identifiant de l'utilisateur */
  userId: string;
  /** Activation des notifications */
  enableNotifications: boolean;
  /** Minutes avant l'événement pour les notifications (ex: [15, 60, 1440] pour 15min, 1h, 1jour) */
  notificationMinutes: number[];
  /** Date de création des paramètres */
  createdAt: Timestamp;
  /** Date de dernière modification */
  updatedAt: Timestamp;
}

/**
 * Interface pour les données d'événement lors de la création (sans les champs auto-générés)
 */
export interface CreateCalendarEventData {
  plantId: string;
  plantName: string;
  treatmentType: TreatmentType;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  isRecurring: boolean;
  recurrencePattern?: RecurrencePattern;
  createdBy: EventCreatedBy;
  status?: EventStatus;
}

/**
 * Interface pour les données de mise à jour d'événement
 */
export interface UpdateCalendarEventData {
  plantName?: string;
  treatmentType?: TreatmentType;
  title?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
  status?: EventStatus;

}

/**
 * Interface pour les données de paramètres de notifications lors de la création
 */
export interface CreateNotificationSettingsData {
  userId: string;
  enableNotifications?: boolean;
  notificationMinutes?: number[];
}

/**
 * Interface pour les données de mise à jour des paramètres de notifications
 */
export interface UpdateNotificationSettingsData {
  enableNotifications?: boolean;
  notificationMinutes?: number[];
}

/**
 * Interface pour les filtres de recherche d'événements
 */
export interface CalendarEventFilters {
  /** Filtrer par plante */
  plantId?: string;
  /** Filtrer par type de traitement */
  treatmentType?: TreatmentType;
  /** Filtrer par statut */
  status?: EventStatus;
  /** Date de début de la période */
  startDate?: Date;
  /** Date de fin de la période */
  endDate?: Date;
  /** Inclure uniquement les événements récurrents */
  recurringOnly?: boolean;
}

/**
 * Interface pour les statistiques du calendrier
 */
export interface CalendarStats {
  /** Nombre total d'événements */
  totalEvents: number;
  /** Nombre d'événements programmés */
  scheduledEvents: number;
  /** Nombre d'événements terminés */
  completedEvents: number;
  /** Nombre d'événements en retard */
  overdueEvents: number;
  /** Nombre d'événements ignorés */
  skippedEvents: number;
  /** Répartition par type de traitement */
  eventsByType: Record<TreatmentType, number>;
}



/**
 * Interface pour les options de vue du calendrier
 */
export interface CalendarViewOptions {
  /** Type de vue (mois, semaine, jour, liste) */
  viewType: 'month' | 'week' | 'day' | 'list';
  /** Date actuellement affichée */
  currentDate: Date;
  /** Filtres appliqués */
  filters: CalendarEventFilters;
  /** Afficher les événements terminés */
  showCompleted: boolean;
  /** Afficher les événements en retard */
  showOverdue: boolean;
}
