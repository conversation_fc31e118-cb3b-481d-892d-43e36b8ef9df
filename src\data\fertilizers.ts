// Base de données des engrais et nutriments
// Basée sur la documentation "Base de Connaissances FloraSynth"

export interface Fertilizer {
  id: string;
  nom: string;
  nom_scientifique?: string;
  type_engrais: 'Minéral' | 'Organique' | 'Organo-minéral';
  composition_npk: string;
  elements_principaux: string[];
  forme_presentation: string;
  description_action: string;
  plantes_cibles_general: string[];
  symptomes_carence_corriges: string[];
  frequence_application_jours: number;
  dosages: {
    methode: string;
    dose: string;
    instructions_application: string;
  }[];
  periode_utilisation_ideale: string[];
  precautions_emploi: string;
  agriculture_biologique_compatible: boolean;
  notes_complementaires?: string;
}

export const FERTILIZERS_DATABASE: Fertilizer[] = [
  {
    id: 'sulfate-de-fer',
    nom: 'Sulfate de Fer (II)',
    nom_scientifique: 'FeSO4·7H2O',
    type_engrais: 'Minéral',
    composition_npk: 'Riche en Fe et S',
    elements_principaux: ['Fer (Fe)', 'Soufre (S)'],
    forme_presentation: 'Poudre ou granulés',
    description_action: 'Lutte contre la chlorose ferrique (jaunissement des feuilles dû à une carence en fer). Le fer est essentiel à la formation de la chlorophylle. Acidifie légèrement le sol.',
    plantes_cibles_general: ['Plantes acidophiles', 'Hortensias', 'Rhododendrons', 'Azalées', 'Camélias', 'Rosiers', 'Arbres fruitiers', 'Gazon'],
    symptomes_carence_corriges: ['Chlorose ferrique (jaunissement des jeunes feuilles, nervures vertes)', 'Croissance ralentie'],
    frequence_application_jours: 15,
    dosages: [
      {
        methode: 'Arrosage au sol',
        dose: '15-20 g/m²',
        instructions_application: 'Mélanger au sol lors du bêchage ou saupoudrer en surface, puis arroser abondamment.'
      },
      {
        methode: 'Pulvérisation foliaire',
        dose: '2-3 g / 1 L d\'eau',
        instructions_application: 'Appliquer directement sur les feuilles jaunes. Éviter de traiter en plein soleil.'
      }
    ],
    periode_utilisation_ideale: ['Printemps', 'Été', 'Dès apparition des symptômes'],
    precautions_emploi: 'Ne pas appliquer sur les dalles (risque de taches de rouille). Peut être irritant, porter des gants. Un surdosage peut brûler les racines.',
    agriculture_biologique_compatible: true
  },
  {
    id: 'sulfate-de-magnesium',
    nom: 'Sulfate de Magnésium (Sel d\'Epsom)',
    nom_scientifique: 'MgSO4·7H2O',
    type_engrais: 'Minéral',
    composition_npk: 'MgO: 16%, SO₃: 32%',
    elements_principaux: ['Magnésium (Mg)', 'Soufre (S)'],
    forme_presentation: 'Cristaux solubles',
    description_action: 'Apporte du magnésium, essentiel à la photosynthèse (composant de la chlorophylle) et à l\'activation de nombreuses enzymes. Action rapide.',
    plantes_cibles_general: ['Tomates', 'Poivrons', 'Rosiers', 'Conifères', 'Légumes feuilles'],
    symptomes_carence_corriges: ['Jaunissement internervaire des feuilles anciennes', 'Nécrose marginale', 'Mauvaise coloration des fruits'],
    frequence_application_jours: 15,
    dosages: [
      {
        methode: 'Arrosage au sol',
        dose: '20 g / 10 L d\'eau',
        instructions_application: 'Arroser au pied des plantes. Répéter toutes les 2-4 semaines si nécessaire.'
      },
      {
        methode: 'Pulvérisation foliaire',
        dose: '1 c.à.c / 1 L d\'eau (5g/L)',
        instructions_application: 'Vaporiser sur le feuillage, de préférence tôt le matin ou tard le soir.'
      }
    ],
    periode_utilisation_ideale: ['Printemps', 'Été', 'Période de croissance active'],
    precautions_emploi: 'Bien diagnostiquer la carence avant application. Un excès de potassium ou de calcium peut inhiber l\'absorption du magnésium.',
    agriculture_biologique_compatible: true
  },
  {
    id: 'sulfate-de-potassium',
    nom: 'Sulfate de Potassium',
    nom_scientifique: 'K2SO4',
    type_engrais: 'Minéral',
    composition_npk: '0-0-50',
    elements_principaux: ['Potassium (K)', 'Soufre (S)'],
    forme_presentation: 'Poudre ou granulés solubles',
    description_action: 'Apporte du potassium, crucial pour la floraison, la fructification, la résistance aux maladies, au gel et à la sécheresse. Améliore la qualité des fruits.',
    plantes_cibles_general: ['Arbres fruitiers', 'Légumes fruits', 'Rosiers', 'Plantes à fleurs', 'Vignes'],
    symptomes_carence_corriges: ['Brunissement des bords de feuilles', 'Fruits de mauvaise qualité', 'Sensibilité aux maladies', 'Mauvaise résistance au froid'],
    frequence_application_jours: 21,
    dosages: [
      {
        methode: 'Arrosage au sol',
        dose: '0.5-1 g par litre d\'eau',
        instructions_application: 'Dissoudre dans l\'eau d\'arrosage et appliquer au pied des plantes.'
      },
      {
        methode: 'Incorporation au sol',
        dose: '20-40 g/m²',
        instructions_application: 'Répartir uniformément et incorporer légèrement au sol, puis arroser.'
      }
    ],
    periode_utilisation_ideale: ['Printemps (avant floraison)', 'Été (pendant fructification)', 'Automne (renforcement)'],
    precautions_emploi: 'Compatible avec la plupart des engrais sauf ceux contenant du calcium. Un excès peut induire des carences en magnésium.',
    agriculture_biologique_compatible: true
  },
  {
    id: 'uree-azote',
    nom: 'Urée (Engrais Azoté)',
    nom_scientifique: 'CO(NH2)2',
    type_engrais: 'Minéral',
    composition_npk: '46-0-0',
    elements_principaux: ['Azote (N)'],
    forme_presentation: 'Granulés ou perlée, très soluble',
    description_action: 'Fournit une forte concentration d\'azote, essentiel pour la croissance végétative et le développement du feuillage.',
    plantes_cibles_general: ['Légumes feuilles', 'Gazon', 'Plantes en croissance active'],
    symptomes_carence_corriges: ['Jaunissement généralisé des feuilles', 'Croissance lente', 'Feuillage clairsemé'],
    frequence_application_jours: 21,
    dosages: [
      {
        methode: 'Arrosage au sol',
        dose: '20-30 g/m²',
        instructions_application: 'Répartir sur sol humide et incorporer légèrement. Arroser après application.'
      },
      {
        methode: 'Pulvérisation foliaire diluée',
        dose: '3-4 g/L (avec précaution)',
        instructions_application: 'Pulvériser tôt le matin ou tard le soir. Éviter par temps chaud.'
      }
    ],
    periode_utilisation_ideale: ['Printemps', 'Début été', 'Période de croissance végétative'],
    precautions_emploi: 'Risque de brûlure en cas de surdosage. Ne pas appliquer par temps très chaud. Peut acidifier le sol.',
    agriculture_biologique_compatible: false
  }
];

// Fonction utilitaire pour rechercher un engrais par symptômes
export const findFertilizersBySymptoms = (symptoms: string[]): Fertilizer[] => {
  return FERTILIZERS_DATABASE.filter(fertilizer =>
    symptoms.some(symptom =>
      fertilizer.symptomes_carence_corriges.some(corrected =>
        corrected.toLowerCase().includes(symptom.toLowerCase())
      )
    )
  );
};

// Fonction utilitaire pour calculer les dosages par contenant
export const calculateDosageForContainer = (baseDose: string, containerSize: string): string => {
  // Extraction du dosage de base (ex: "2-3g/L" -> 2.5g/L)
  const match = baseDose.match(/(\d+(?:\.\d+)?)-?(\d+(?:\.\d+)?)?.*?\/.*?(\d+(?:\.\d+)?)\s*L/i);
  if (!match) return baseDose;

  const minDose = parseFloat(match[1]);
  const maxDose = match[2] ? parseFloat(match[2]) : minDose;
  const baseVolume = parseFloat(match[3]);
  const avgDose = (minDose + maxDose) / 2;

  const containerVolume = parseFloat(containerSize.replace(/[^\d.]/g, ''));
  const calculatedDose = (avgDose * containerVolume) / baseVolume;

  return `${calculatedDose.toFixed(1)}g`;
};

// Fonction pour obtenir les engrais recommandés selon le type de plante
export const getFertilizersByPlantType = (plantName: string): Fertilizer[] => {
  const lowerPlantName = plantName.toLowerCase();

  return FERTILIZERS_DATABASE.filter(fertilizer =>
    fertilizer.plantes_cibles_general.some(target =>
      target.toLowerCase().includes(lowerPlantName) ||
      lowerPlantName.includes(target.toLowerCase())
    )
  );
};

// Fonction pour identifier les carences par symptômes visuels
export const identifyDeficiencyBySymptoms = (symptoms: string[]): { element: string; fertilizers: Fertilizer[] }[] => {
  const deficiencies = [
    {
      element: 'Azote (N)',
      keywords: ['jaunissement généralisé', 'croissance lente', 'feuilles anciennes jaunes'],
      fertilizerIds: ['uree-azote', 'sang-seche', 'corne-broyee']
    },
    {
      element: 'Fer (Fe)',
      keywords: ['chlorose ferrique', 'jeunes feuilles jaunes', 'nervures vertes'],
      fertilizerIds: ['sulfate-de-fer']
    },
    {
      element: 'Magnésium (Mg)',
      keywords: ['jaunissement internervaire', 'nervures vertes', 'nécrose marginale'],
      fertilizerIds: ['sulfate-de-magnesium']
    },
    {
      element: 'Potassium (K)',
      keywords: ['bords feuilles brûlés', 'fruits mauvaise qualité', 'sensibilité maladies'],
      fertilizerIds: ['sulfate-de-potassium']
    }
  ];

  return deficiencies
    .filter(def =>
      symptoms.some(symptom =>
        def.keywords.some(keyword =>
          symptom.toLowerCase().includes(keyword.toLowerCase())
        )
      )
    )
    .map(def => ({
      element: def.element,
      fertilizers: FERTILIZERS_DATABASE.filter(f => def.fertilizerIds.includes(f.id))
    }));
};
