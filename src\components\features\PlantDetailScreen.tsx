import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { getPlant, getDiagnosticRecords, deletePlant } from '@/services/api';
import { Plant, DiagnosticRecord, GeminiDiagnosis } from '@/types';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Spinner } from '@/components/common/Spinner';
import { PlusIcon, LeafIcon } from '@/components/common/icons';
import { NewDiagnostic } from '../NewDiagnostic';
import { motion, AnimatePresence } from 'framer-motion';
import { Timestamp } from 'firebase/firestore';

const DiagnosticDetailModal = ({ record, onClose }: { record: DiagnosticRecord | null, onClose: () => void }) => {
    if (!record) return null;

    // Fonction utilitaire pour convertir le timestamp en Date de manière sécurisée
    const getDateFromTimestamp = (timestamp: any): Date => {
        if (timestamp && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
        }
        if (timestamp instanceof Date) {
            return timestamp;
        }
        if (timestamp && timestamp.seconds) {
            return new Date(timestamp.seconds * 1000);
        }
        return new Date();
    };

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0.9 }}
                    className="bg-[#1c1a31] p-6 rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                >
                    <h2 className="text-2xl font-bold text-white mb-2">Diagnostic : {record.diagnosis.disease}</h2>
                    <p className="text-sm text-gray-400 mb-4">
                        {getDateFromTimestamp(record.timestamp).toLocaleString()}
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        {record.imageUrls.map((url, index) => (
                            <img key={index} src={url} alt={`Diagnosis image ${index + 1}`} className="rounded-lg w-full object-cover" />
                        ))}
                    </div>
                    <div className="space-y-4 text-[#E0E0E0]">
                        <p>{record.diagnosis.description}</p>
                        <div>
                            <h3 className="font-bold text-white text-lg mb-2">Plan de Traitement</h3>
                            <ul className="list-disc list-inside space-y-1">
                                {record.diagnosis.treatmentPlan.steps.map((step, i) => <li key={i}>{step}</li>)}
                            </ul>
                        </div>
                        <div>
                            <h3 className="font-bold text-white text-lg mb-2">Produits Recommandés</h3>
                            <ul className="list-disc list-inside space-y-1">
                                {record.diagnosis.treatmentPlan.recommendedProducts.map((prod, i) => (
                                    <li key={i}>
                                        {typeof prod === 'string' ? prod : prod.name || 'Produit non spécifié'}
                                    </li>
                                ))}
                            </ul>
                        </div>
                         <div>
                            <h3 className="font-bold text-white text-lg mb-2">Conseils de Soin Généraux</h3>
                            <ul className="list-disc list-inside space-y-1">
                                {record.diagnosis.careTips.map((tip, i) => <li key={i}>{tip}</li>)}
                            </ul>
                        </div>
                    </div>
                    <div className="text-right mt-6">
                       <Button onClick={onClose} variant="secondary">Fermer</Button>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

interface DiagnosticHistoryCardProps {
    record: DiagnosticRecord;
    onSelect: (record: DiagnosticRecord) => void;
}

const DiagnosticHistoryCard: React.FC<DiagnosticHistoryCardProps> = ({ record, onSelect }) => {
    const { diagnosis, timestamp, nextTreatmentDate } = record;

    // Fonction utilitaire pour convertir le timestamp en Date de manière sécurisée
    const getDateFromTimestamp = (timestamp: any): Date => {
        if (timestamp && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
        }
        if (timestamp instanceof Date) {
            return timestamp;
        }
        if (timestamp && timestamp.seconds) {
            return new Date(timestamp.seconds * 1000);
        }
        return new Date();
    };

    const getTreatmentStatus = () => {
        if (!nextTreatmentDate) return { text: 'Aucun traitement programmé', color: 'text-gray-400' };

        try {
            const now = new Date();
            const nextDate = getDateFromTimestamp(nextTreatmentDate);
            const diffTime = nextDate.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays < 0) return { text: `Traitement en retard de ${Math.abs(diffDays)} jour(s)`, color: 'text-red-400' };
            if (diffDays === 0) return { text: 'Traitement dû aujourd\'hui', color: 'text-yellow-400' };
            return { text: `Prochain traitement dans ${diffDays} jour(s)`, color: 'text-green-400' };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
            console.error('❌ Erreur lors du calcul du statut de traitement:', errorMessage);
            return { text: 'Erreur de calcul de date', color: 'text-gray-400' };
        }
    };

    const status = getTreatmentStatus();

    return (
        <Card className="w-full" onClick={() => onSelect(record)}>
            <div className="flex justify-between items-start">
                <div>
                    <h3 className="font-bold text-white text-lg">{diagnosis.disease}</h3>
                    <p className="text-sm text-gray-400">{getDateFromTimestamp(timestamp).toLocaleDateString()}</p>
                </div>
                <span className={`px-3 py-1 text-xs font-semibold rounded-full ${diagnosis.isHealthy ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                    {diagnosis.isHealthy ? 'En Bonne Santé' : 'Nécessite des Soins'}
                </span>
            </div>
            <p className="mt-2 text-sm text-[#E0E0E0] truncate">{diagnosis.description}</p>
            {nextTreatmentDate && <p className={`mt-2 text-sm font-semibold ${status.color}`}>{status.text}</p>}
        </Card>
    );
};

const PlantDetailScreen: React.FC = () => {
    const { plantId } = useParams<{ plantId: string }>();
    const navigate = useNavigate();
    const { user } = useAuth();

    const [plant, setPlant] = useState<Plant | null>(null);
    const [records, setRecords] = useState<DiagnosticRecord[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState<DiagnosticRecord | null>(null);

    useEffect(() => {
        if (user && plantId) {
            const fetchPlantData = async () => {
                const plantData = await getPlant(user.uid, plantId);
                setPlant(plantData);
            };

            fetchPlantData();
            const unsubscribe = getDiagnosticRecords(user.uid, plantId, (fetchedRecords) => {
                setRecords(fetchedRecords);
                setIsLoading(false);
            });

            return () => unsubscribe();
        }
    }, [user, plantId]);

    const sortedRecords = useMemo(() => {
        return records.sort((a, b) => {
            // Fonction utilitaire pour obtenir le timestamp en millisecondes
            const getTimestampMillis = (timestamp: any): number => {
                if (timestamp && typeof timestamp.toMillis === 'function') {
                    return timestamp.toMillis();
                }
                if (timestamp instanceof Date) {
                    return timestamp.getTime();
                }
                if (timestamp && timestamp.seconds) {
                    return timestamp.seconds * 1000;
                }
                return 0;
            };

            return getTimestampMillis(b.timestamp) - getTimestampMillis(a.timestamp);
        });
    }, [records]);

    const handleDelete = async () => {
        if (user && plantId && window.confirm("Êtes-vous sûr de vouloir supprimer cette plante et tout son historique ? Cette action ne peut pas être annulée.")) {
            try {
                await deletePlant(user.uid, plantId);
                navigate('/');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
                console.error("❌ Error deleting plant: ", errorMessage);
                alert("Échec de la suppression de la plante. Veuillez réessayer.");
            }
        }
    };
    
    if (isLoading) return <div className="flex items-center justify-center h-screen"><Spinner size="lg" /></div>;
    if (!plant) return <div className="text-center py-20 text-white text-2xl">Plante non trouvée.</div>;
    
    if (isAnalyzing) {
        return <NewDiagnostic plant={plant} onFinish={() => setIsAnalyzing(false)} />;
    }

    return (
        <div className="p-4 sm:p-8">
            <div className="flex flex-col sm:flex-row justify-between items-start mb-8 gap-4">
                <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                        <LeafIcon className="w-12 h-12 text-[#d385f5]" />
                    </div>
                    <div>
                        <h1 className="text-4xl font-bold text-white">{plant.name}</h1>
                        <p className="text-[#E0E0E0]">{plant.species}</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button onClick={() => setIsAnalyzing(true)}>
                        <PlusIcon className="w-5 h-5 mr-2 inline" /> Nouveau Diagnostic
                    </Button>
                     <Button onClick={handleDelete} variant="secondary" className="border-red-500 text-red-400 hover:bg-red-500/10 focus:ring-red-500">
                        Supprimer la Plante
                    </Button>
                </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6">Historique des Diagnostics</h2>
            {sortedRecords.length > 0 ? (
                 <motion.div 
                    className="space-y-4"
                    initial="hidden"
                    animate="visible"
                    variants={{
                        visible: { transition: { staggerChildren: 0.1 } }
                    }}
                 >
                    {sortedRecords.map(record => (
                        <DiagnosticHistoryCard key={record.id} record={record} onSelect={setSelectedRecord} />
                    ))}
                 </motion.div>
            ) : (
                <div className="text-center py-16 bg-[#1c1a31] rounded-2xl">
                    <h3 className="text-xl text-white">Aucun historique pour le moment.</h3>
                    <p className="text-[#E0E0E0] mt-2">Commencez un nouveau diagnostic pour vérifier la santé de votre plante.</p>
                </div>
            )}
            <DiagnosticDetailModal record={selectedRecord} onClose={() => setSelectedRecord(null)} />
        </div>
    );
};

export default PlantDetailScreen;