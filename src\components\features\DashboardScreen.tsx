import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { getPlants, addPlant } from '@/services/api';
import { Plant } from '@/types';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Spinner } from '@/components/common/Spinner';
import { PlusIcon, LeafIcon } from '@/components/common/icons';
import { FertilizerGuide } from '@/components/FertilizerGuide';
import { motion, AnimatePresence } from 'framer-motion';
import { serverTimestamp } from 'firebase/firestore';

const AddPlantModal = ({ isOpen, onClose, onAdd }: { isOpen: boolean, onClose: () => void, onAdd: (name: string, species: string, description?: string) => void }) => {
    const [name, setName] = useState('');
    const [species, setSpecies] = useState('');
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        await onAdd(name, species, description);
        setIsLoading(false);
        setName('');
        setSpecies('');
        setDescription('');
        onClose();
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9, y: -20 }}
                    animate={{ scale: 1, y: 0 }}
                    exit={{ scale: 0.9, y: -20 }}
                    className="bg-[#1c1a31] p-8 rounded-2xl w-full max-w-md"
                    onClick={(e) => e.stopPropagation()}
                >
                    <h2 className="text-2xl font-bold text-white mb-6">Ajouter une Nouvelle Plante</h2>
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <label htmlFor="plant-name" className="block text-sm font-medium text-[#E0E0E0] mb-2">Nom de la Plante</label>
                            <input
                                type="text"
                                id="plant-name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                required
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Monstera Deliciosa"
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="plant-species" className="block text-sm font-medium text-[#E0E0E0] mb-2">Espèce (Optionnel)</label>
                            <input
                                type="text"
                                id="plant-species"
                                value={species}
                                onChange={(e) => setSpecies(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none"
                                placeholder="ex: Araceae"
                            />
                        </div>
                        <div className="mb-6">
                            <label htmlFor="plant-description" className="block text-sm font-medium text-[#E0E0E0] mb-2">Description de l'état actuel</label>
                            <textarea
                                id="plant-description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                className="w-full bg-[#100f1c] border border-gray-600 rounded-lg p-3 text-white focus:ring-2 focus:ring-[#a364f7] focus:outline-none h-24 resize-none"
                                placeholder="Décrivez ce que vous observez sur votre plante : couleur des feuilles, état général, problèmes éventuels, etc."
                            />
                            <p className="text-xs text-gray-400 mt-1">
                                💡 Cette description aidera l'IA à mieux analyser votre plante lors des futurs diagnostics
                            </p>
                        </div>
                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="secondary" onClick={onClose}>Annuler</Button>
                            <Button type="submit" isLoading={isLoading}>Ajouter la Plante</Button>
                        </div>
                    </form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};


const DashboardScreen: React.FC = () => {
    const { user } = useAuth();
    const navigate = useNavigate();
    const [plants, setPlants] = useState<Plant[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isFertilizerGuideOpen, setIsFertilizerGuideOpen] = useState(false);

    useEffect(() => {
        if (user) {
            const unsubscribe = getPlants(user.uid, (fetchedPlants) => {
                setPlants(fetchedPlants);
                setIsLoading(false);
            });
            return () => unsubscribe();
        }
    }, [user]);

    const handleAddPlant = async (name: string, species: string, description?: string) => {
        if (user) {
            await addPlant(user.uid, {
                name,
                species,
                description: description || '',
                createdAt: serverTimestamp()
            });
        }
    };

    if (isLoading) {
        return <div className="flex items-center justify-center h-screen"><Spinner size="lg" /></div>;
    }
    
    return (
        <div className="p-4 sm:p-8">
            <h1 className="text-4xl font-bold text-white mb-8">Mon Jardin</h1>
            <AnimatePresence>
                {plants.length > 0 ? (
                    <motion.div 
                        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                        initial="hidden"
                        animate="visible"
                        variants={{
                            visible: { transition: { staggerChildren: 0.1 } }
                        }}
                    >
                        {plants.map(plant => (
                            <Card key={plant.id} onClick={() => navigate(`/plant/${plant.id}`)}>
                                <div className="flex flex-col items-center text-center">
                                    <div className="p-3 mb-4 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                                       <LeafIcon className="w-12 h-12 text-[#d385f5]" />
                                    </div>
                                    <h3 className="text-xl font-bold text-white">{plant.name}</h3>
                                    {plant.species && <p className="text-sm text-[#E0E0E0]">{plant.species}</p>}
                                </div>
                            </Card>
                        ))}
                    </motion.div>
                ) : (
                    <motion.div initial={{opacity: 0}} animate={{opacity: 1}} className="text-center py-20">
                        <h2 className="text-2xl font-semibold text-white mb-4">Votre jardin est vide !</h2>
                        <p className="text-[#E0E0E0] mb-6">Ajoutez votre première plante pour commencer avec FloraSynth.</p>
                        <Button onClick={() => setIsModalOpen(true)}>
                            <PlusIcon className="w-5 h-5 mr-2 inline" /> Ajouter une Plante
                        </Button>
                    </motion.div>
                )}
            </AnimatePresence>

            <button
                onClick={() => setIsModalOpen(true)}
                className="fixed bottom-8 right-8 bg-gradient-to-r from-[#d385f5] to-[#a364f7] text-white p-4 rounded-full shadow-lg transform transition-transform hover:scale-110"
                aria-label="Ajouter une nouvelle plante"
            >
                <PlusIcon className="w-8 h-8" />
            </button>
            
            <AddPlantModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onAdd={handleAddPlant}
            />

            <FertilizerGuide
                isOpen={isFertilizerGuideOpen}
                onToggle={() => setIsFertilizerGuideOpen(!isFertilizerGuideOpen)}
            />
        </div>
    );
};

export default DashboardScreen;