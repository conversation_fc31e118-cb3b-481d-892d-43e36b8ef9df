import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { User } from 'firebase/auth';
import { auth } from '@/services/api';

export interface AuthContextType {
  user: User | null;
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log("🔄 Initialisation de l'AuthContext...");

    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      console.log("🔄 Changement d'état d'authentification détecté");

      setUser(currentUser);
      setLoading(false);

      // Log détaillé pour débugger
      if (currentUser) {
        console.log("✅ AuthContext: Utilisateur connecté");
        console.log("👤 Nom:", currentUser.displayName);
        console.log("📧 Email:", currentUser.email);
        console.log("🔑 UID:", currentUser.uid);
        console.log("📍 URL actuelle:", window.location.href);
      } else {
        console.log("❌ AuthContext: Utilisateur déconnecté");
        console.log("📍 URL actuelle:", window.location.href);
      }
    });

    return () => {
      console.log("🧹 Nettoyage de l'AuthContext");
      unsubscribe();
    };
  }, []);

  const value = {
    user,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
