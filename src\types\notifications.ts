import { Timestamp } from 'firebase/firestore';
import { TreatmentType } from './calendar';

/**
 * Types d'événements de diagnostic
 */
export type DiagnosticEventType = 'diagnostic' | 'traitement' | 'suivi' | 'rappel';

/**
 * Priorité des notifications
 */
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Statut d'un événement de diagnostic
 */
export type DiagnosticEventStatus = 'pending' | 'completed' | 'skipped' | 'overdue';

/**
 * Interface pour un événement de diagnostic avec notifications
 */
export interface DiagnosticEvent {
  /** Identifiant unique de l'événement */
  id: string;
  /** Identifiant de la plante concernée */
  plantId: string;
  /** Nom de la plante pour affichage */
  plantName: string;
  /** Identifiant de l'utilisateur */
  userId: string;
  /** Type d'événement */
  eventType: DiagnosticEventType;
  /** Type de traitement associé */
  treatmentType: TreatmentType;
  /** Titre de l'événement */
  title: string;
  /** Description détaillée */
  description: string;
  /** Date de création de l'événement */
  dateCreated: Timestamp;
  /** Date prévue pour la prochaine action */
  nextActionDate: Timestamp;
  /** Type de la prochaine action */
  nextActionType: string;
  /** Recommandation de Gemini */
  geminiRecommendation: string;
  /** Priorité de la notification */
  priority: NotificationPriority;
  /** Statut de l'événement */
  status: DiagnosticEventStatus;
  /** Indique si l'événement est terminé */
  completed: boolean;
  /** Date de création */
  createdAt: Timestamp;
  /** Date de dernière modification */
  updatedAt: Timestamp;
  /** Identifiant du diagnostic original */
  originalDiagnosticId: string;
}

/**
 * Interface pour créer un nouvel événement de diagnostic
 */
export interface CreateDiagnosticEventData {
  plantId: string;
  plantName: string;
  userId: string;
  eventType: DiagnosticEventType;
  treatmentType: TreatmentType;
  title: string;
  description: string;
  nextActionDate: Date;
  nextActionType: string;
  geminiRecommendation: string;
  priority?: NotificationPriority;
  originalDiagnosticId: string;
}

/**
 * Interface pour mettre à jour un événement de diagnostic
 */
export interface UpdateDiagnosticEventData {
  title?: string;
  description?: string;
  nextActionDate?: Date;
  nextActionType?: string;
  geminiRecommendation?: string;
  priority?: NotificationPriority;
  status?: DiagnosticEventStatus;
  completed?: boolean;
}

/**
 * Interface pour une notification utilisateur
 */
export interface UserNotification {
  /** Identifiant unique de la notification */
  id: string;
  /** Identifiant de l'utilisateur */
  userId: string;
  /** Identifiant de l'événement de diagnostic associé */
  diagnosticEventId: string;
  /** Titre de la notification */
  title: string;
  /** Message de la notification */
  message: string;
  /** Priorité de la notification */
  priority: NotificationPriority;
  /** Indique si la notification a été lue */
  read: boolean;
  /** Date de création de la notification */
  createdAt: Timestamp;
  /** Date de lecture (si applicable) */
  readAt?: Timestamp;
  /** Date d'expiration de la notification */
  expiresAt?: Timestamp;
}

/**
 * Interface pour créer une nouvelle notification
 */
export interface CreateNotificationData {
  userId: string;
  diagnosticEventId: string;
  title: string;
  message: string;
  priority: NotificationPriority;
  expiresAt?: Date;
}

/**
 * Interface pour l'historique des actions utilisateur
 */
export interface UserActionHistory {
  /** Identifiant unique de l'action */
  id: string;
  /** Identifiant de l'utilisateur */
  userId: string;
  /** Identifiant de la plante concernée */
  plantId: string;
  /** Nom de la plante */
  plantName: string;
  /** Type d'action effectuée */
  actionType: 'diagnostic' | 'treatment_applied' | 'event_completed' | 'event_skipped';
  /** Description de l'action */
  description: string;
  /** Date de l'action */
  actionDate: Timestamp;
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
  /** Date de création */
  createdAt: Timestamp;
}

/**
 * Interface pour créer une entrée d'historique
 */
export interface CreateActionHistoryData {
  userId: string;
  plantId: string;
  plantName: string;
  actionType: 'diagnostic' | 'treatment_applied' | 'event_completed' | 'event_skipped';
  description: string;
  metadata?: Record<string, any>;
}

/**
 * Interface pour les statistiques de notifications
 */
export interface NotificationStats {
  /** Nombre total de notifications */
  totalNotifications: number;
  /** Nombre de notifications non lues */
  unreadNotifications: number;
  /** Nombre d'événements en attente */
  pendingEvents: number;
  /** Nombre d'événements en retard */
  overdueEvents: number;
  /** Répartition par priorité */
  notificationsByPriority: Record<NotificationPriority, number>;
  /** Répartition par type d'événement */
  eventsByType: Record<DiagnosticEventType, number>;
}

/**
 * Interface pour les filtres de recherche d'événements
 */
export interface DiagnosticEventFilters {
  /** Filtrer par plante */
  plantId?: string;
  /** Filtrer par type d'événement */
  eventType?: DiagnosticEventType;
  /** Filtrer par statut */
  status?: DiagnosticEventStatus;
  /** Filtrer par priorité */
  priority?: NotificationPriority;
  /** Date de début de la période */
  startDate?: Date;
  /** Date de fin de la période */
  endDate?: Date;
  /** Inclure uniquement les événements non terminés */
  pendingOnly?: boolean;
}

/**
 * Interface pour les paramètres de notification Gemini
 */
export interface GeminiNotificationSettings {
  /** Activation des recommandations automatiques */
  enableAutoRecommendations: boolean;
  /** Fréquence de vérification en heures */
  checkFrequencyHours: number;
  /** Activation des alertes de sécurité */
  enableSafetyAlerts: boolean;
  /** Seuil de priorité minimum pour les notifications */
  minimumPriority: NotificationPriority;
}
