# Configuration Firebase
# Obtenez ces valeurs depuis votre console Firebase
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Configuration Google Gemini
# Obtenez votre clé API depuis Google AI Studio
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Configuration de l'environnement
NODE_ENV=development

# Instructions :
# 1. Copiez ce fichier vers .env.local
# 2. Remplacez les valeurs par vos vraies clés API
# 3. Ne commitez JAMAIS le fichier .env.local (il est dans .gitignore)
